import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { FV } from "../[[...path]]/containers/edit-content-page";

export const SeoSuggestButton = ({ form, title, content, language }: {
  form: any;
  title: string;
  content: string;
  language: string;
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const getSuggestion = async () => {
    setIsLoading(true);
    try {
      const res = await fetch("/api/assistance/seo", {
        method: "POST",
        body: JSON.stringify({
          title,
          article: content,
          lang: language,
        }),
      });
      const data = await res.json();

      const fieldConfig: {
        label: string;
        formFieldKey: keyof FV;
        dataKey: keyof typeof data;
      }[] = [
        {
          label: "URL slug",
          formFieldKey: "slug",
          dataKey: "slug",
        },
        {
          label: "Title",
          formFieldKey: "title",
          dataKey: "title",
        },
        {
          label: "Description",
          formFieldKey: "seoDescription",
          dataKey: "description",
        },
      ];

      // Show suggestions in toast
      const suggestions = fieldConfig.map(({ label, formFieldKey, dataKey }) =>
        `${label}: ${data[dataKey]}`
      ).join('\n\n');

      toast({
        title: "SEO Suggestions",
        description: suggestions,
        duration: 10000,
      });

      // Auto-apply suggestions to form
      fieldConfig.forEach(({ formFieldKey, dataKey }) => {
        form.setFieldValue(formFieldKey, data[dataKey]);
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button disabled={isLoading} onClick={getSuggestion}>
      {isLoading ? "Loading..." : "SEO suggestions"}
    </Button>
  );
};
