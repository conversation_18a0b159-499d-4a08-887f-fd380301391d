import { BlogListResponse } from "@/app/api/manage/blog/list/route";
import { Edit } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useNavigate } from "react-router-dom";

export const RenderTitle = ({ record }: { record: BlogListResponse[0] }) => {
  const navigate = useNavigate();
  const { contents } = record;

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2 relative">
        <Avatar className="flex-shrink-0 rounded-md">
          <AvatarImage src={contents[0].imageUrl || undefined} />
          <AvatarFallback className="rounded-md">IMG</AvatarFallback>
        </Avatar>
        <div>
          {contents.map((entry) => {
            const { title, language, id, isReady } = entry;
            const inCompleteFields = Object.entries({
              ...entry,
              ...record,
            }).filter(([, value]) => value === "");
            return (
              <div
                key={language}
                className="flex gap-1 group cursor-pointer"
                onClick={() => navigate(`/edit/${id}`)}
              >
                <div className={"line-clamp-1"}>
                  <span>[{language}] </span>
                  {!isReady && (
                    <span className="text-yellow-500">[draft] </span>
                  )}{" "}
                  {title}
                </div>
                {!!inCompleteFields.length && (
                  <Tooltip>
                    <TooltipTrigger>
                      <span>⚠️</span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>
                        Empty fields:{" "}
                        <b>{inCompleteFields.map(([key]) => key).join(", ")}</b>
                      </span>
                    </TooltipContent>
                  </Tooltip>
                )}
                <Edit className="h-4 w-4 invisible group-hover:visible" />
              </div>
            );
          })}
        </div>
      </div>
    </TooltipProvider>
  );
};
