"use client";
import { EditContentPage } from "../../[[...path]]/containers/edit-content-page";
import { useParams } from "next/navigation";
import dynamic from "next/dynamic";

function EditContentPageRoute() {
  const params = useParams();
  const { contentId } = params;

  return <EditContentPage contentId={contentId as string} />;
}

export default dynamic(() => Promise.resolve(EditContentPageRoute), {
  ssr: false,
});