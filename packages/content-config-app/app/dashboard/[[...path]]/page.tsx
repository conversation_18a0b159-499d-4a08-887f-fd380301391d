"use client";
import dynamic from "next/dynamic";
import { createBrowser<PERSON>outer, RouterProvider } from "react-router-dom";
import { DashboardHomePage } from "./containers/dashboard-home-page";
import { EditContentPage } from "./containers/edit-content-page";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";

const queryClient = new QueryClient();

function DashboardPage() {
  const router = createBrowserRouter(
    [
      {
        path: "/",
        element: <DashboardHomePage />,
      },
      {
        path: "/edit/:contentId?",
        element: <EditContentPage />,
      },
    ],
    { basename: "/dashboard" },
  );

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-background">
        <main className="p-12 py-4 flex-auto flex flex-col">
          <RouterProvider router={router} />
        </main>
        <Toaster />
      </div>
    </QueryClientProvider>
  );
}

export default dynamic(() => Promise.resolve(DashboardPage), {
  ssr: false,
});
