"use client";
import dynamic from "next/dynamic";
import { DashboardHomePage } from "./containers/dashboard-home-page";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";

const queryClient = new QueryClient();

function DashboardPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-background">
        <main className="p-12 py-4 flex-auto flex flex-col">
          <DashboardHomePage />
        </main>
        <Toaster />
      </div>
    </QueryClientProvider>
  );
}

export default dynamic(() => Promise.resolve(DashboardPage), {
  ssr: false,
});
