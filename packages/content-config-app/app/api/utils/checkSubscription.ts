import { auth } from "@/auth";
import Stripe from "stripe";

export const getSubscriptionPermissions = async () => {
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
  const session = await auth();
  const { stripeCustomerId } = session?.user ?? {};
  if (!stripeCustomerId) {
    return {};
  }
  const res = await stripe.subscriptions.list({
    customer: stripeCustomerId,
    status: "active",
  });

  const permissions = res.data.reduce((acc: Record<string, boolean>, entry) => {
    const metadata = entry.items.data[0].plan?.metadata ?? {};
    const keys = Object.keys(metadata);
    keys.forEach((key) => {
      acc[key] = metadata[key] === "true";
    });
    return acc;
  }, {});

  return permissions;
};

export const checkSubscription = async (category: string) => {
  const permissions = await getSubscriptionPermissions();

  if (permissions[category]) {
    return true;
  }

  const session = await auth();
  const { role } = session?.user ?? {};
  if (role === "ADMIN") {
    return true;
  }
  return false;
};
