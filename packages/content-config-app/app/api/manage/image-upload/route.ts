import { ImageBucket } from "ui/src/utils";
import { NextResponse } from "next/server";

const { S3_BUCKET_NAME, AWS_REGION } = process.env;

export async function POST(req: Request) {
  const formData = await req.formData();

  const { searchParams } = new URL(req.url);
  const key = searchParams.get("key");

  const file = formData.get("file") as File;

  if (!file) {
    return NextResponse.json({ error: "No files received." }, { status: 400 });
  }

  if (!file.type.match(/(jpg|jpeg|png|webp)$/i)) {
    return NextResponse.json(
      { error: "Only jpg,png,webp formats are allowed!" },
      { status: 400 },
    );
  }

  const fileKey = key || file.name;
  const bucket = new ImageBucket();

  try {
    // Upload with 10MB size limit
    await bucket.uploadFile({
      key: fileKey,
      file,
      maxSizeBytes: 10 * 1024 * 1024
    });

    return NextResponse.json({
      message: "File uploaded successfully.",
      url: `https://${S3_BUCKET_NAME}.s3.${AWS_REGION}.amazonaws.com/blog-image/${fileKey}`,
    });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Upload failed" },
      { status: 400 }
    );
  }
}
