import { prisma } from "prisma-db";

const spaceId = process.env.SPACE_ID ?? "";

export const queryCategoriesByLanguage = (language: string = "en") => {
  console.log({ language });
  return prisma.category.findMany({
    where: { spaceId },
    orderBy: { id: "desc" },
    include: {
      labels: true,
      _count: {
        select: {
          blogs:
            language === "all"
              ? true
              : { where: { contents: { some: { language } } } },
        },
      },
    },
  });
};

export type CategoryGetResponse = Awaited<
  ReturnType<typeof queryCategoriesByLanguage>
>;
