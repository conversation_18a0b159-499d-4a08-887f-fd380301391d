"use server";
import { auth } from "@/auth";
import { SignIn, SignOut } from "./auth-buttons";
import Link from "next/link";

export const Nav = async () => {
  const res = await auth();
  const user = res?.user;
  const isAdmin = user?.role === "ADMIN";

  return (
    <nav className="flex-shrink-0 flex items-center justify-between min-h-[64px] px-6 py-3 bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50">
      <div className="text-xl font-semibold text-gray-800">
        <Link
          href="/dashboard"
          className="hover:text-blue-600 transition-colors"
        >
          Content Manager
        </Link>
      </div>

      <div className="flex gap-4 items-center">
        {res?.user ? (
          <>
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-600">
                {res.user.name || res.user.email}
              </span>
              {isAdmin && (
                <Link
                  href="/dashboard"
                  className="text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium"
                >
                  Dashboard
                </Link>
              )}
            </div>
            <SignOut />
          </>
        ) : null}
      </div>
    </nav>
  );
};
