import {
  DeleteObjectCommand,
  ListObjectsV2Command,
  PutO<PERSON>Command,
  S3Client,
  S3ServiceException,
} from "@aws-sdk/client-s3";

/**
 * Upload a file to an S3 bucket.
 */

const { S3_BUCKET_NAME, AWS_REGION, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY } =
  process.env;

export class ImageBucket {
  private client: S3Client | null = null;

  private getClient(): S3Client {
    if (this.client) {
      return this.client;
    }

    if (
      !S3_BUCKET_NAME ||
      !AWS_REGION ||
      !AWS_ACCESS_KEY_ID ||
      !AWS_SECRET_ACCESS_KEY
    ) {
      throw new Error("Missing environment variables for S3 configuration");
    }

    this.client = new S3Client({
      region: AWS_REGION,
      credentials: {
        accessKeyId: AWS_ACCESS_KEY_ID,
        secretAccessKey: AWS_SECRET_ACCESS_KEY,
      },
    });

    return this.client;
  }

  listFiles = async () => {
    const command = new ListObjectsV2Command({
      Bucket: S3_BUCKET_NAME,
    });

    const response = await this.getClient().send(command);
    console.log(response);
    return response;
  };

  uploadFile = async ({
    key,
    file,
    maxSizeBytes = 5 * 1024 * 1024, // Default 5MB limit
  }: {
    key: string;
    file: File;
    maxSizeBytes?: number;
  }) => {
    // Validate file size
    const fileSize = file.size || (await file.arrayBuffer?.())?.byteLength || 0;
    if (fileSize > maxSizeBytes) {
      throw new Error(
        `File size ${(fileSize / 1024 / 1024).toFixed(
          2,
        )}MB exceeds maximum allowed size ${(
          maxSizeBytes /
          1024 /
          1024
        ).toFixed(2)}MB`,
      );
    }

    const buffer =
      file instanceof Buffer ? file : Buffer.from(await file.arrayBuffer());

    await this.upload({ key: `blog-image/${key}`, buffer });
  };

  upload = async ({ key, buffer }: { key: string; buffer: Buffer }) => {
    const command = new PutObjectCommand({
      Bucket: S3_BUCKET_NAME,
      Key: key,
      Body: buffer,
    });

    try {
      const response = await this.getClient().send(command);
      console.log(response);
    } catch (caught) {
      if (
        caught instanceof S3ServiceException &&
        caught.name === "EntityTooLarge"
      ) {
        console.error(
          `Error from S3 while uploading object to ${S3_BUCKET_NAME}. \
  The object was too large. To upload objects larger than 5GB, use the S3 console (160GB max) \
  or the multipart upload API (5TB max).`,
        );
      } else if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while uploading object to ${S3_BUCKET_NAME}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };

  deleteFile = async ({ key }: { key: string }) => {
    const command = new DeleteObjectCommand({
      Bucket: S3_BUCKET_NAME,
      Key: key,
    });

    try {
      const response = await this.getClient().send(command);
      console.log(response);
    } catch (caught) {
      if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while deleting object to ${S3_BUCKET_NAME}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };
}
