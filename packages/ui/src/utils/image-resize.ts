export class ImageResize {
  private quality: number;
  private width: number;
  private height: number | undefined;
  constructor({
    width,
    height,
    quality,
  }: {
    width: number;
    quality?: number;
    height?: number;
  }) {
    this.quality = quality ?? 0.85;
    this.width = width;
    this.height = height;
  }

  async resizeFile(file: File | Blob | undefined | null) {
    return new Promise<Blob | null>((resolve, reject) => {
      const reader = new FileReader();
      const img = new Image();
      const MAX_WIDTH = this.width;
      const MAX_HEIGHT = this.height || this.width;
      const QUALITY = this.quality;

      reader.onload = function (e: ProgressEvent<FileReader>) {
        if (typeof e.target?.result === "string") {
          img.src = e.target.result;
        } else {
          reject("failed to read file");
        }
      };
      img.onload = function () {
        let width = img.width;
        let height = img.height;
        if (width > height) {
          if (width > MAX_WIDTH) {
            height *= MAX_WIDTH / width;
            width = MAX_WIDTH;
          }
        } else {
          if (height > MAX_HEIGHT) {
            width *= MAX_HEIGHT / height;
            height = MAX_HEIGHT;
          }
        }

        // Create canvas and draw the resized image
        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          return reject("get canvas failed");
        }
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to WebP and show the preview
        canvas.toBlob(resolve, "image/webp", QUALITY);
      };

      if (file) {
        reader.readAsDataURL(file);
      } else {
        reject("no file input");
      }
    });
  }

  async resizeToMultipleSizes(
    file: File | Blob | undefined | null,
    sizes: { width: number; height?: number; suffix: string }[]
  ): Promise<{ blob: Blob; suffix: string }[]> {
    if (!file) {
      throw new Error("No file provided");
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      const img = new Image();

      reader.onload = (e: ProgressEvent<FileReader>) => {
        if (typeof e.target?.result === "string") {
          img.src = e.target.result;
        } else {
          reject(new Error("Failed to read file"));
        }
      };

      img.onload = async () => {
        try {
          const results: { blob: Blob; suffix: string }[] = [];

          for (const size of sizes) {
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");

            if (!ctx) {
              throw new Error("Could not get canvas context");
            }

            // Calculate dimensions maintaining aspect ratio
            let { width, height } = this.calculateDimensions(
              img.width,
              img.height,
              size.width,
              size.height || size.width
            );

            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to blob
            const blob = await new Promise<Blob | null>((blobResolve) => {
              canvas.toBlob(blobResolve, "image/jpeg", 0.8);
            });

            if (blob) {
              results.push({ blob, suffix: size.suffix });
            }
          }

          resolve(results);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error("Failed to load image"));
      reader.readAsDataURL(file);
    });
  }

  private calculateDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    let width = originalWidth;
    let height = originalHeight;

    if (width > height) {
      if (width > maxWidth) {
        height *= maxWidth / width;
        width = maxWidth;
      }
    } else {
      if (height > maxHeight) {
        width *= maxHeight / height;
        height = maxHeight;
      }
    }

    return { width: Math.round(width), height: Math.round(height) };
  }

  // Utility function to upload multiple sizes
  static async uploadMultipleSizes(
    originalFile: File,
    baseKey: string,
    uploadEndpoint: string = "/api/manage/image-upload"
  ): Promise<{ original: string; resized: { [key: string]: string } }> {
    const imageResize = new ImageResize({ width: 1200 });

    // Define the sizes we want to create
    const sizes = [
      { width: 1200, suffix: "1200" },
      { width: 800, suffix: "800" },
      { width: 400, suffix: "400" },
    ];

    try {
      // Resize to multiple sizes
      const resizedImages = await imageResize.resizeToMultipleSizes(originalFile, sizes);

      const uploadPromises: Promise<{ key: string; url: string }>[] = [];
      const results: { original: string; resized: { [key: string]: string } } = {
        original: "",
        resized: {}
      };

      // Upload original file
      const originalFormData = new FormData();
      originalFormData.append("file", originalFile);
      uploadPromises.push(
        fetch(`${uploadEndpoint}?key=${encodeURIComponent(baseKey)}`, {
          method: "POST",
          body: originalFormData,
        })
        .then(res => res.json())
        .then(data => ({ key: "original", url: data.url }))
      );

      // Upload resized versions
      for (const { blob, suffix } of resizedImages) {
        const formData = new FormData();
        const fileName = `${baseKey.replace(/\.[^/.]+$/, "")}-${suffix}.jpg`;
        formData.append("file", new File([blob], fileName, { type: "image/jpeg" }));

        uploadPromises.push(
          fetch(`${uploadEndpoint}?key=${encodeURIComponent(fileName)}`, {
            method: "POST",
            body: formData,
          })
          .then(res => res.json())
          .then(data => ({ key: suffix, url: data.url }))
        );
      }

      // Wait for all uploads to complete
      const uploadResults = await Promise.all(uploadPromises);

      for (const result of uploadResults) {
        if (result.key === "original") {
          results.original = result.url;
        } else {
          results.resized[result.key] = result.url;
        }
      }

      return results;
    } catch (error) {
      console.error("Failed to upload multiple sizes:", error);
      throw error;
    }
  }
}
