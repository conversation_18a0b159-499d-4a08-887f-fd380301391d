"use client";
// InitializedMDXEditor.tsx
import "@mdxeditor/editor/style.css";
import "./overrides.css";

import {
  BlockTypeSelect,
  BoldItalicUnderlineToggles,
  ChangeCodeMirrorLanguage,
  codeBlockPlugin,
  codeMirrorPlugin,
  ConditionalContents,
  CreateLink,
  diffSourcePlugin,
  DiffSourceToggleWrapper,
  type DirectiveDescriptor,
  directivesPlugin,
  GenericDirectiveEditor,
  headingsPlugin,
  imagePlugin,
  InsertAdmonition,
  InsertCodeBlock,
  InsertImage,
  InsertTable,
  InsertThematicBreak,
  linkDialogPlugin,
  linkPlugin,
  listsPlugin,
  ListsToggle,
  markdownShortcutPlugin,
  MDXEditor,
  type MDXEditorMethods,
  type MDXEditorProps,
  quotePlugin,
  Separator,
  tablePlugin,
  thematicBreakPlugin,
  toolbarPlugin,
  UndoRedo,
} from "@mdxeditor/editor";
import type { ForwardedRef } from "react";

import { cx, ImageResize, useDarkMode } from "../utils";

const CalloutDirectiveDescriptor: DirectiveDescriptor = {
  name: "callout",
  testNode(node) {
    return node.name === "callout";
  },
  // set some attribute names to have the editor display a property editor popup.
  attributes: [],
  // used by the generic editor to determine whether or not to render a nested editor.
  hasChildren: true,
  Editor: GenericDirectiveEditor,
};

// Only import this to the next file
export default function InitializedMDXEditor({
  editorRef,
  previousValue,
  blogId,
  ...props
}: {
  editorRef: ForwardedRef<MDXEditorMethods> | null;
  previousValue?: string;
  blogId?: string;
} & MDXEditorProps) {
  async function imageUploadHandler(file: File) {
    try {
      const fileKey = `${blogId ? `${blogId}/` : ""}${Date.now()}-${file.name}`;

      // Upload multiple sizes and return the 1200px version URL for the editor
      const uploadResults = await ImageResize.uploadMultipleSizes(file, fileKey);

      // Return the resized 1200px version if available, otherwise the original
      return uploadResults.resized["1200"] || uploadResults.original;
    } catch (error) {
      console.error("Image upload failed:", error);
      return "";
    }
  }

  const isDarkMode = useDarkMode();

  return (
    <MDXEditor
      className={cx(isDarkMode && "dark-theme dark-editor")}
      plugins={[
        listsPlugin(),
        headingsPlugin(),
        linkPlugin(),
        linkDialogPlugin(),
        imagePlugin({ imageUploadHandler, disableImageResize: true }),
        diffSourcePlugin({
          diffMarkdown: previousValue,
          viewMode: "rich-text",
        }),
        quotePlugin(),
        tablePlugin(),
        thematicBreakPlugin(),
        directivesPlugin({
          directiveDescriptors: [CalloutDirectiveDescriptor],
        }),
        codeBlockPlugin({ defaultCodeBlockLanguage: "js" }),
        codeMirrorPlugin({
          codeBlockLanguages: { js: "JavaScript", css: "CSS" },
        }),
        markdownShortcutPlugin(),
        toolbarPlugin({
          toolbarContents: () => (
            <>
              <div className="min-h-[34px]" />
              <DiffSourceToggleWrapper>
                <ConditionalContents
                  options={[
                    {
                      when: (editor) => editor?.editorType === "codeblock",
                      contents: () => <ChangeCodeMirrorLanguage />,
                    },
                    {
                      fallback: () => (
                        <>
                          <UndoRedo />
                          <Separator />
                          <BoldItalicUnderlineToggles />
                          <Separator />
                          <ListsToggle />
                          <Separator />
                          <BlockTypeSelect />
                          <Separator />
                          <CreateLink />
                          <InsertImage />
                          <Separator />
                          <InsertTable />
                          <InsertThematicBreak />
                          <Separator />
                          <InsertAdmonition />
                          <InsertCodeBlock />
                        </>
                      ),
                    },
                  ]}
                />
              </DiffSourceToggleWrapper>
            </>
          ),
        }),
      ]}
      {...props}
      ref={editorRef}
    />
  );
}
