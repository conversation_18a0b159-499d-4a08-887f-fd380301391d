import { prisma } from "prisma-db";

const spaceId = process.env.SPACE_ID ?? "";

export const queryArticleList = (
  lang: string | undefined | null,
  category?: string | undefined | null,
) => {
  console.log({ lang, category });
  return prisma.blogContent.findMany({
    where: {
      language: lang || "en",
      isReady: true,
      Blog: {
        spaceId,
        isPublished: true,
        categories: category ? { some: { id: category } } : undefined,
      },
    },
    include: { Blog: true },
    orderBy: { Blog: { articleDate: "desc" } },
  });
};

export type ArticleListResponse = Awaited<ReturnType<typeof queryArticleList>>;
