import { prisma } from "prisma-db";

export const queryArticleBySlug = (slug: string, lang: string | null) =>
  prisma.blogContent.findFirst({
    where: {
      language: lang || "en",
      Blog: { slug, spaceId: process.env.SPACE_ID },
    },
    include: {
      Blog: {
        include: {
          Space: true,
        },
      },
    },
  });

export const queryArticleById = (id: string, lang: string | null) =>
  prisma.blogContent.findFirst({
    where: {
      language: lang || "en",
      Blog: { spaceId: process.env.SPACE_ID },
      id,
    },
    include: {
      Blog: {
        include: {
          Space: true,
        },
      },
    },
  });

export const queryArticleListForStaticParams = () =>
  prisma.blog.findMany({
    where: { isPublished: true },
    include: {
      contents: {
        where: { isReady: true },
      },
    },
  });
