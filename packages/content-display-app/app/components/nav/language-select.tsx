import { LANG_PATHS } from "@/app/utils";
import Link from "next/link";
import { IoLanguage } from "react-icons/io5";

export const LanguageSelect = () => {
  return (
    <div className="group relative">
      <button>
        <IoLanguage />
      </button>

      <div className="hidden group-hover:flex flex-col absolute top-full right-0 border rounded-md bg-background w-24 p-1">
        {LANG_PATHS.map(({ label, path }) => (
          <Link
            key={path}
            className="block p-2 hover:bg-gray-100 text-center roudned"
            href={path}
          >
            {label}
          </Link>
        ))}
      </div>
    </div>
  );
};
