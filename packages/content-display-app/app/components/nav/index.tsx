"use server";
import { auth } from "@/auth";
import { Space } from "prisma-db";
import { ManageBilling } from "../stripe/manage-billing";
import { SignIn, SignOut } from "../auth-buttons";
import { HomeLink } from "./home-link";
import { getSubscriptionPermissions } from "@/app/api/utils/checkSubscription";
import Link from "next/link";

export const Nav = async ({ space }: { space?: Space | null }) => {
  const { title } = space ?? {};

  const res = await auth();
  const user = res?.user;
  const isAdmin = user?.role === "ADMIN";
  // const isSubscribed = user?.subscriptionStatus;

  const permissions = await getSubscriptionPermissions();
  const isSubscribed = !!permissions.portfolio_technology;

  return (
    <nav className="flex-shrink-0 flex items-center justify-between min-h-[40px] px-6 py-4 bg-background sticky top-0 z-10">
      <div className="text-2xl font-bold">
        <HomeLink>{title}</HomeLink>
      </div>

      <div className="flex gap-4 items-center">
        {isSubscribed && <ManageBilling />}
        {res?.user ? (
          <>
            {isAdmin && <Link href="/dashboard">Dashboard</Link>}
            {isAdmin && <Link href="subscribe">Subscribe</Link>}
            <SignOut />
          </>
        ) : (
          <SignIn />
        )}
      </div>
    </nav>
  );
};
