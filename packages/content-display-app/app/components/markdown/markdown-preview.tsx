"use client";
import "highlight.js/styles/atom-one-dark.min.css";

import { useRef } from "react";
import { FaTerminal } from "react-icons/fa6";
import Markdown, { Components } from "react-markdown";
import rehypeRaw from "rehype-raw";
import { IconType } from "react-icons";
import { SiJavascript, SiPython, SiReact } from "react-icons/si";
import { CopyButton } from "./copy-button";
import { cx } from "ui/src/utils/cx";

export const icons: Record<string, IconType | undefined> = {
  js: SiJavascript,
  react: SiReact,
  py: SiPython,
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const Pre: Components["pre"] = ({ node, ...props }) => {
  return <pre {...props} className="not-prose" />;
};

const Code: Components["code"] = ({ node, className, children }) => {
  const contentRef = useRef<HTMLDivElement>(null);

  const match = /language-(\w+)/.exec(className || "");
  if (match?.length) {
    const Icon = icons[match[1]] ?? FaTerminal;

    return (
      <div className="bg-card text-gray-300 border rounded-md overflow-hidden">
        <div className="px-5 py-2 border-b flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon />
            <span>{node?.data?.meta}</span>
          </div>
          <CopyButton element={contentRef} />
        </div>
        <div className="overflow-x-auto w-full">
          <div className="p-5" ref={contentRef}>
            {children}
          </div>
        </div>
      </div>
    );
  }
  return <code className="bg-zinc-700">{children}</code>;
};

export const MarkdownPreview: React.FC<{
  content: string;
  className?: string;
}> = ({ content, className }) => {
  return (
    <Markdown
      className={cx("prose dark:prose-invert max-w-none", className)}
      rehypePlugins={[rehypeRaw]}
      components={{
        pre: Pre,
        code: Code,
      }}
    >
      {content}
    </Markdown>
  );
};
