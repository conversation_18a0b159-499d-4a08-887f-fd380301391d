import { useEffect, useRef, useState } from "react";

export const useDarkMode = () => {
  const darkMode = useRef(matchMedia("(prefers-color-scheme: dark)"));

  const [isDarkMode, setIsDarkMode] = useState(darkMode.current.matches);

  useEffect(() => {
    const callback = () => setIsDarkMode(darkMode.current.matches);
    const media = darkMode.current;

    media.addEventListener("change", callback);

    return () => {
      media.removeEventListener("change", callback);
    };
  }, []);

  return isDarkMode;
};
