import { signIn, signOut } from "@/auth";

export async function SignIn() {
  return (
    <form
      action={async () => {
        "use server";
        await signIn("google", { redirectTo: "/dashboard" });
      }}
    >
      <button className="cursor-pointer flex items-center gap-2" type="submit">
        <span>login</span>
      </button>
    </form>
  );
}

export async function SignOut() {
  return (
    <form
      action={async () => {
        "use server";
        await signOut({ redirectTo: "/" });
      }}
    >
      <button className="cursor-pointer" type="submit">
        Sign-out
      </button>
    </form>
  );
}
