import { Footer } from "./components/footer";
import { Nav } from "./components/nav";
import "./globals.css";
import { querySpace } from "./api/read/space/query";
import { GoogleAnalytics } from "@next/third-parties/google";

const { GA_MEASUREMENT_ID = "" } = process.env;

export async function generateMetadata() {
  const data = await querySpace();

  if (!data) {
    return {};
  }

  const { title, description } = data;

  return {
    title: title,
    description: description,
    openGraph: {
      title: title,
      siteName: title,
      type: "website",
    },
  };
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const space = await querySpace();

  return (
    <html lang="en">
      <link rel="icon" href={process.env.NEXT_PUBLIC_FAVICON_URL} />
      <GoogleAnalytics gaId={GA_MEASUREMENT_ID} />
      <body className={"antialiased"}>
        <div className="h-fit min-h-full flex flex-col">
          <Nav space={space} />
          <div className="flex-auto flex">{children}</div>
          <Footer space={space} />
        </div>
      </body>
    </html>
  );
}
