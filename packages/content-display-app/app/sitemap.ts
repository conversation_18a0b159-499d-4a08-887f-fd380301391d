import { prisma } from "prisma-db";
import { MetadataRoute } from "next";

const SITE_URL = process.env.SITE_URL!;
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const urls: MetadataRoute.Sitemap = [
    {
      url: SITE_URL,
      lastModified: new Date(),
      alternates: {
        languages: {
          en: SITE_URL,
          ja: SITE_URL + "/ja",
        },
      },
    },
  ];

  const articles = await prisma.blog.findMany({
    where: { isPublished: true },
    include: { contents: true },
  });

  articles.forEach(({ slug, contents }) => {
    const languages: { [key: string]: string } = {};

    const articleUrl = [SITE_URL, "article", slug].join("/");

    contents.forEach(({ language }) => {
      if (language === "en") {
        languages["en"] = articleUrl;
      } else {
        languages[language] = [SITE_URL, language, "article", slug].join("/");
      }
    });
    urls.push({
      url: articleUrl,
      lastModified: new Date(),
      alternates: { languages },
    });
  }, []);

  return urls;
}
