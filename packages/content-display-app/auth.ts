import NextAuth, { NextAuthResult } from "next-auth";
import { prisma } from "prisma-db";
import { PrismaAdapter } from "@auth/prisma-adapter";
import type { User } from "prisma-db";
import authConfig from "./auth.config";

const nextAuthResult = NextAuth({
  adapter: PrismaAdapter(prisma),
  ...authConfig,
});

export const signIn: NextAuthResult["signIn"] = nextAuthResult.signIn;
export const auth: NextAuthResult["auth"] = nextAuthResult.auth;
export const { signOut, handlers } = nextAuthResult;

declare module "next-auth" {
  interface Session {
    user: User;
  }
}
