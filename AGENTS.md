# AGENTS.md

用問答的形式幫我完成計劃
把規劃和進程記錄感 md 檔案
使用第三方庫時，用 context7 mcp 來確認使用方式
如果需要，請使狦 create-next-app 命令來建立 next.js 項目
使用 shadcn-ui mcp 來實現組件庫

## Build/Lint/Test Commands
- `pnpm dev` - Start all apps in development mode
- `pnpm dev:display` - Start content display app only (port 3000)
- `pnpm dev:config` - Start content config app only (port 3001)
- `pnpm build` - Build all packages
- `pnpm lint` - Lint all packages
- `pnpm type-check` - Type check all packages
- No test files found in codebase

## Code Style Guidelines
- Use double quotes for strings
- Semicolons required
- Trailing commas for all applicable items
- 2 space indentation
- 80 character line limit
- Named exports preferred over default exports
- TypeScript types required for all functions and components
- Use PascalCase for components and interfaces
- Use camelCase for variables and functions
- Error handling with try/catch for async operations
- Import paths use workspace aliases when possible
- Shared components in `ui` package
- Database operations through `prisma` package

## Formatting
- Prettier formatting enforced via ESLint
- Next.js linting rules with core-web-vitals
- React prop-types disabled
- Console warnings enabled