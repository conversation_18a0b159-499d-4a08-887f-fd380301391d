# Next.js Blog Application - Turborepo Monorepo

This project has been split into a Turborepo monorepo with separate applications for content display and content management.

## 🏗️ Project Structure

```
next-blog-app/
├── packages/
│   ├── content-display-app/     # Public blog (port 3000)
│   ├── content-config-app/      # Admin dashboard (port 3001)
│   ├── ui/                      # Shared components & utilities
│   ├── prisma/                  # Database schema & client
│   ├── typescript-config/       # Shared TS configurations
│   └── scripts/                 # Shared scripts
├── turbo.json                   # Turborepo configuration
├── pnpm-workspace.yaml          # PNPM workspace config
└── package.json                 # Root package with Turborepo scripts
```

## 📦 Applications

### Content Display App (Port 3000)
- **Purpose**: Public-facing blog content display
- **Features**: Article listing, article pages, subscription checkout
- **URL**: http://localhost:3000

### Content Config App (Port 3001)
- **Purpose**: Admin dashboard for content management
- **Features**: Article creation/editing, user management, analytics
- **URL**: http://localhost:3001

## 🚀 Development Commands

### Start Both Applications
```bash
pnpm dev
```

### Start Individual Applications
```bash
# Content display app only
pnpm dev:display

# Content config app only
pnpm dev:config
```

### Build Commands
```bash
# Build all packages
pnpm build

# Build specific app
pnpm build --filter=content-display-app
pnpm build --filter=content-config-app
```

### Other Commands
```bash
# Lint all packages
pnpm lint

# Type check all packages
pnpm type-check

# Clean all build artifacts
pnpm clean

# Database operations
pnpm db:generate
pnpm db:push
pnpm db:migrate
```

## 🛠️ Development Setup

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Set up environment variables**:
   Create `.env` files in both app directories with:
   ```
   DATABASE_URL="your-database-url"
   NEXTAUTH_SECRET="your-nextauth-secret"
   STRIPE_SECRET_KEY="your-stripe-secret-key"
   AWS_ACCESS_KEY_ID="your-aws-access-key-id"
   AWS_SECRET_ACCESS_KEY="your-aws-secret-access-key"
   OPENAI_API_KEY="your-openai-api-key"
   ```

3. **Run database migrations**:
   ```bash
   pnpm db:migrate
   ```

4. **Start development**:
   ```bash
   pnpm dev
   ```

## 📁 Shared Packages

### UI Package (`packages/ui`)
- Shared React components
- Utility functions (cx, image-resize, s3, openai, stripe)
- Common styling utilities

### Prisma Package (`packages/prisma`)
- Database schema definitions
- Prisma client configuration
- Database migrations

### TypeScript Config (`packages/typescript-config`)
- Shared TypeScript configurations
- Base, Next.js, and React library configs

## 🔧 Package Management

This project uses **pnpm** with workspaces. All dependencies are managed through the workspace system:

- Shared dependencies are in the root `package.json`
- App-specific dependencies are in each package's `package.json`
- Workspace packages reference each other using `workspace:*`

## 📝 Adding New Features

1. **Shared utilities**: Add to `packages/ui/src/utils/`
2. **Shared components**: Add to `packages/ui/src/components/`
3. **Database changes**: Update schema in `packages/prisma/schema/`
4. **Public features**: Add to `packages/content-display-app/`
5. **Admin features**: Add to `packages/content-config-app/`

## 🚢 Deployment

Each application can be deployed independently:

```bash
# Build for production
pnpm build --filter=content-display-app
pnpm build --filter=content-config-app

# Start production servers
cd packages/content-display-app && pnpm start
cd packages/content-config-app && pnpm start
```

## 🔍 Troubleshooting

- **Import errors**: Check that shared packages are properly exported in their `index.ts` files
- **Build failures**: Ensure all dependencies are installed with `pnpm install`
- **Port conflicts**: Apps run on ports 3000 and 3001 by default
- **Database issues**: Run `pnpm db:generate` after schema changes
