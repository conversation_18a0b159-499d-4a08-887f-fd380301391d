# CRUSH.md

## Build/Lint/Test Commands
- `pnpm dev` - Start all apps in development mode
- `pnpm dev:display` - Start content display app only (port 3000)
- `pnpm dev:config` - Start content config app only (port 3001)
- `pnpm build` - Build all packages
- `pnpm lint` - Lint all packages
- `pnpm type-check` - Type check all packages
- No test files found in codebase

## Code Style Guidelines
- Use double quotes for strings
- Semicolons required
- Trailing commas for all applicable items
- 2 space indentation
- 80 character line limit
- Named exports preferred over default exports
- TypeScript types required for all functions and components
- Use PascalCase for components and interfaces
- Use camelCase for variables and functions
- Error handling with try/catch for async operations
- Import paths use workspace aliases when possible
- Shared components in `ui` package
- Database operations through `prisma` package

## Formatting
- Prettier formatting enforced via ESLint
- Next.js linting rules with core-web-vitals
- React prop-types disabled
- Console warnings enabled

## Additional Rules
- Use Context7 MCP when using third-party libraries
- Use shadcn-ui MCP for component library implementation
- Database operations must go through the `prisma` package
- Shared components should be in the `ui` package
- Use workspace aliases for import paths when possible